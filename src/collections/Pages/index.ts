import { CollectionConfig, TypedUser } from 'payload'
import isAdminOnly from '@/payloadCMS/roleaccess/isAdminOnly'
import isFieldAdminOnly from '@/payloadCMS/roleaccess/isFieldAdminOnly'
import formatSlug from '@/utilities/payload/formatSlug'
import { Content } from '../Posts/blocks/Content'
import { DownloadLock } from '../Posts/blocks/DownloadLock'
import { FileDownloadLock } from '../Posts/blocks/FileDownloadLock'
import { YouTubeEmbed } from '../Posts/blocks/YouTubeEmbed'

const Pages: CollectionConfig = {
  slug: 'pages',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'publishDate', 'status'],
    group: 'Content',
  },
  access: {
    read: isAdminOnly,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  fields: [
    {
      name: 'title',
      label: 'Title',
      type: 'text',
      required: true,
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      filterOptions: {
        archived: { equals: false },
      },
      hasMany: false,
    },
    {
      name: 'tags',
      type: 'relationship',
      relationTo: 'tags',
      filterOptions: {
        archived: { equals: false },
      },
      hasMany: true,
    },
    {
      name: 'image',
      label: 'Featured Image',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'summary',
      type: 'richText',
      label: 'Summary (keep it short, tldr style)',
    },
    {
      name: 'layout',
      label: 'Page Layout',
      type: 'blocks',
      minRows: 1,
      // the blocks are reusable objects that will be added in array to the document, these are especially useful for structuring content purpose built for frontend componentry
      blocks: [Content, YouTubeEmbed, DownloadLock, FileDownloadLock],
    },
    {
      name: 'slug',
      label: 'Slug',
      type: 'text',
      unique: true,
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [formatSlug('title')],
      },
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      defaultValue: ({ user }: { user: TypedUser }) => user.id,
      admin: {
        position: 'sidebar',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'publishDate',
      type: 'date',
      admin: {
        position: 'sidebar',
        description: 'Posts will not be public until this date',
      },
      defaultValue: () => new Date(),
    },
  ],
}

export default Pages
