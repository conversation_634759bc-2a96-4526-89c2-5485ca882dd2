import { <PERSON>ada<PERSON> } from 'next'
import { notFound } from 'next/navigation'
import { getPayload } from 'payload'
import { cache } from 'react'
import { Layout } from '@/collections/Posts'
import { formatTimestamp } from '@/utilities/local/date'
import { asyncErrorBoundary } from '@/utilities/local/pageErrorHandling/asyncErrorBoundary'
import config from '@payload-config'
import PagesDAO from '../_backend/common/dao/PagesDAO'
import { NotFound } from '../_backend/common/exception'
import PagesService from '../_backend/common/service/PagesService'
import BlockRender from '../_component/BlockRender'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import BlogContainer from '../_cssComp/BlogContainer'
import Container from '../_cssComp/Container'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import MainFooter from '../_templates/MainFooter'
import MainNavMenu from '../_templates/MainNavMenu'

let pagesService: PagesService | null = null

async function getPagesService() {
  if (!pagesService) {
    const payload = await getPayload({ config })
    const pagesDAO = new PagesDAO(payload)
    pagesService = new PagesService(pagesDAO)
  }
  return pagesService
}

const getPagePost = cache(async (slug: string) => {
  const service = await getPagesService()
  const blogPost = await service.getPageBySlug(slug)
  if (!blogPost) {
    console.error('Page not found:', slug)
    throw new NotFound('Page not found') // Prevent caching null
  }
  return blogPost
})

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const page = await getPagePost((await params).slug)
  if (page == null) {
    return {
      title: 'Not Found',
    }
  }
  return {
    title: page.meta?.title,
    description: page.meta?.description,
  }
}

async function Page({ params }: { params: Promise<{ slug: string }> }) {
  const pageSlug = (await params).slug
  const pagePost = await getPagePost(pageSlug)

  if (pagePost == null || pagePost.id == null) {
    return notFound()
  }

  return (
    <div className={`flex flex-col min-h-screen w-full`}>
      <div className="bg-main sticky top-0 w-full z-400">
        <MainNavMenu />
      </div>
      <BlogContainer className="flex-auto">
        <Container>
          <FlexContainer
            direction={FlexDirection.COL}
            align={AlignItems.CENTER}
            justify={JustifyContent.CENTER}
          >
            <FlexContainer
              direction={FlexDirection.COL}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
              className="w-full gap-2"
            >
              <Title level={HeaderLevel.H1} className="text-center">
                {pagePost.title}
              </Title>
              <Text variant="description">Last updated: {formatTimestamp(pagePost.updatedAt)}</Text>
            </FlexContainer>
          </FlexContainer>
          <div className={`w-full mt-4`}>
            {pagePost.layout && pagePost.layout.length > 0 && (
              <BlockRender layout={pagePost.layout as Layout[]} className="" />
            )}
          </div>
        </Container>
      </BlogContainer>
      <div className="bg-main bottom-0 w-full">
        <MainFooter />
      </div>
    </div>
  )
}

export default asyncErrorBoundary(Page)
