# This workflow will build image and push to image registry
name: Continuous Deployment

permissions:
  contents: write

on:
  push:
    branches: [ "master" ]

env:
  PG_USERNAME: postgres
  PG_PASSWORD: testpass
  PG_HOST: postgres
  PG_PORT: 5432
  PG_DATABASE: line-art-generator
  DOCKER_NETWORK: cd-network
  APP_NAME: coloraria

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        # This is the crucial line that fetches all history and tags
        fetch-depth: 0

    - name: Format Branch Name
      run: |
        BRANCH_CLEAN=$(echo "${{ github.ref_name }}" | tr '/' '-')
        echo "CLEAN_BRANCH=$BRANCH_CLEAN" >> $GITHUB_ENV

    - name: Fetch Tags
      run: git fetch --tags --force
    
    - name: Generate Semantic Version
      id: version
      uses: paulhatch/semantic-version@v5.4.0
      with:
        # The prefix to use to identify tags
        tag_prefix: "v"
        # A string which, if present in a git commit, indicates a breaking change
        major_pattern: "^(major):"
        # A string which, if present in a git commit, indicates a new feature
        minor_pattern: "^(feat|feature):"
        # If true, the body of commits will also be searched for major/minor patterns
        search_commit_body: true
        # The version format
        version_format: "v${major}.${minor}.${patch}"
        bump_each_commit: false

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_TOKEN }}

    - name: Create Docker network
      run: docker network create ${{ env.DOCKER_NETWORK }}

    - name: Run PostgreSQL container
      run: |
        docker run -d \
          --name ${{ env.PG_HOST }} \
          --network ${{ env.DOCKER_NETWORK }} \
          -e POSTGRES_USER=${{ env.PG_USERNAME }} \
          -e POSTGRES_PASSWORD=${{ env.PG_PASSWORD }} \
          -e POSTGRES_DB=${{ env.PG_DATABASE }} \
          postgres

    - name: Create Docker buildx builder
      run: |
        docker buildx create --name image-builder --driver docker-container --use --driver-opt network=${{ env.DOCKER_NETWORK }}

    - name: Bootstrap buildx builder
      run: docker buildx inspect --bootstrap

    - name: Build Docker image with buildx
      run: |
        POSTGRES_IP=$(docker inspect -f '{{(index .NetworkSettings.Networks "${{ env.DOCKER_NETWORK }}").IPAddress}}' ${{ env.PG_HOST }})
        docker buildx build \
          --add-host=postgres:$POSTGRES_IP \
          --build-arg PG_USERNAME=${{ env.PG_USERNAME }} \
          --build-arg PG_PASSWORD=${{ env.PG_PASSWORD }} \
          --build-arg PG_HOST=${{ env.PG_HOST }} \
          --build-arg PG_PORT=${{ env.PG_PORT }} \
          --build-arg PG_DATABASE=${{ env.PG_DATABASE }} \
          -t ${{ secrets.DOCKER_USERNAME }}/${{ env.APP_NAME }}:${{ steps.version.outputs.version }} \
          -t ${{ secrets.DOCKER_USERNAME }}/${{ env.APP_NAME }}:latest . --push

    - uses: webfactory/ssh-agent@v0.4.1
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
    - run: mkdir -p ~/.ssh/ && ssh-keyscan -H ${{ secrets.SERVER_IPV4 }} >> ~/.ssh/known_hosts
    - run: |
        ssh ${{ vars.WORKER }}@${{ secrets.SERVER_IPV4 }} \
        "cd line-art-generator-v2 && \
        git checkout master && \
        git pull origin master && \
        export DOCKER_USERNAME=${{ secrets.DOCKER_USERNAME }} && \
        export DOCKER_TOKEN=${{ secrets.DOCKER_TOKEN }} && \
        export APP_NAME=${{ env.APP_NAME }} && \
        scripts/run.sh"

    - name: Push Release Tag
      if: contains(fromJson('["master"]'), github.ref_name)
      run: |
        git tag ${{ steps.version.outputs.version }}
        git push origin ${{ steps.version.outputs.version }}
