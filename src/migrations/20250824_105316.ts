import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "subscription_plans" ADD COLUMN "image_cap" numeric NOT NULL;`)

  await db.execute(sql`
    UPDATE "subscription_plans" SET "monthly_credit_stipend" = 0;
  `)

  await db.execute(sql`
    ALTER TABLE "subscription_plans" ALTER COLUMN "monthly_credit_stipend" SET NOT NULL;
  `)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "subscription_plans" DROP COLUMN "image_cap";`)
}
